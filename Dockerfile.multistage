# Multi-stage Dockerfile
# Stage 1: File preparation
FROM busybox:1.35 as file-prep
WORKDIR /files

# Copy all EVD files and keys
COPY *EVD* ./evd/
COPY *.key ./keys/

# Create directory structure script
RUN echo '#!/bin/sh' > setup.sh && \
    echo 'mkdir -p /app/vouchermanagement/Key' >> setup.sh && \
    echo 'mkdir -p /app/vouchermanagement/voucherfiles/input' >> setup.sh && \
    echo 'mkdir -p /app/vouchermanagement/voucherfiles/input/backup' >> setup.sh && \
    echo 'cp -r /files/evd/* /app/vouchermanagement/voucherfiles/input/ 2>/dev/null || true' >> setup.sh && \
    echo 'cp -r /files/keys/* /app/vouchermanagement/Key/ 2>/dev/null || true' >> setup.sh && \
    echo 'chmod -R 755 /app/vouchermanagement' >> setup.sh && \
    echo 'echo "Files copied successfully"' >> setup.sh && \
    echo 'ls -la /app/vouchermanagement/voucherfiles/input/' >> setup.sh && \
    echo 'ls -la /app/vouchermanagement/Key/' >> setup.sh && \
    chmod +x setup.sh

# Stage 2: Main application
FROM eclipse-temurin:21-jre-alpine

# Timezone
ENV TZ=Africa/Nairobi
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Rudementary
WORKDIR /app

# Application name same as deployment name
LABEL name="one-platform-evd-distribution-management-jobs" \
description="RMS OP Job for voucher processing and activation" \
maintainer="IT Delivery Team"
MAINTAINER "IT Delivery Team"

# Copy files from file-prep stage
COPY --from=file-prep /files /files

# Create necessary directories to match volume mount paths
RUN mkdir -p /app/vouchermanagement/Key && \
    mkdir -p /app/vouchermanagement/voucherfiles/input && \
    mkdir -p /app/vouchermanagement/voucherfiles/input/backup && \
    chmod -R 755 /app/vouchermanagement

# Port
EXPOSE 8082

# Add App
ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} /app/service.jar

# User mode
USER root

ENTRYPOINT ["java","-jar","/app/service.jar"]
