#!/bin/bash

echo "=== Available Storage Classes ==="
kubectl get storageclass

echo ""
echo "=== Default Storage Class ==="
kubectl get storageclass -o jsonpath='{.items[?(@.metadata.annotations.storageclass\.kubernetes\.io/is-default-class=="true")].metadata.name}'

echo ""
echo "=== Storage Class Details ==="
kubectl describe storageclass

echo ""
echo "=== Existing PVCs in oneplatform namespace ==="
kubectl get pvc -n oneplatform

echo ""
echo "=== Available PVs ==="
kubectl get pv
