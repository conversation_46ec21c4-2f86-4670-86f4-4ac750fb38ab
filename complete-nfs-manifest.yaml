# PersistentVolume for NFS
apiVersion: v1
kind: PersistentVolume
metadata:
  name: nfs-voucher-pv
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: nfs
  nfs:
    server: mdc1-psc-nfs.safaricomet.net
    path: /oneplatform
---
# PersistentVolumeClaim for NFS
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nfs-voucher-pvc
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: nfs
  volumeName: nfs-voucher-pv
---
# Deployment with PVC
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: oneplatform
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: oneplatform-bp-secret
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:8
          imagePullPolicy: Always
          ports:
            - containerPort: 8082
          volumeMounts:
            - name: nfs-voucher-storage
              mountPath: /app/vouchermanagement
              subPath: vouchermanagement
          env:
            - name: TZ
              value: "Africa/Nairobi"
      volumes:
        - name: nfs-voucher-storage
          persistentVolumeClaim:
            claimName: nfs-voucher-pvc
---
# Service
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
