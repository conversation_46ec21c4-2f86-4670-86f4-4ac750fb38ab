# PersistentVolumeClaim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-storage-pvc
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
# ConfigMap to hold EVD files (if they are small text files)
apiVersion: v1
kind: ConfigMap
metadata:
  name: evd-files-config
  namespace: oneplatform
data:
  # Add your EVD files here as key-value pairs
  # Example:
  # EVD001.txt: |
  #   content of EVD001.txt file
  # EVD002.txt: |
  #   content of EVD002.txt file
  setup-directories.sh: |
    #!/bin/bash
    echo "Setting up directory structure..."
    mkdir -p /app/vouchermanagement/Key
    mkdir -p /app/vouchermanagement/voucherfiles/input
    mkdir -p /app/vouchermanagement/voucherfiles/input/backup
    chmod -R 755 /app/vouchermanagement
    
    # Copy EVD files if they exist in configmap
    if [ -d "/evd-files" ]; then
      echo "Copying EVD files..."
      cp /evd-files/EVD* /app/vouchermanagement/voucherfiles/input/ 2>/dev/null || echo "No EVD files found in configmap"
    fi
    
    echo "Directory structure created successfully:"
    ls -la /app/vouchermanagement/
    ls -la /app/vouchermanagement/voucherfiles/
    ls -la /app/vouchermanagement/voucherfiles/input/
---
# Deployment with init container
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: oneplatform
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: oneplatform-bp-secret
      # Init container to set up directories and copy files
      initContainers:
        - name: setup-directories
          image: busybox:1.35
          command: ["/bin/sh", "/scripts/setup-directories.sh"]
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
            - name: setup-scripts
              mountPath: /scripts
            - name: evd-files
              mountPath: /evd-files
          securityContext:
            runAsUser: 0
            runAsGroup: 0
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:8
          imagePullPolicy: Always
          ports:
            - containerPort: 8082
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
          env:
            - name: TZ
              value: "Africa/Nairobi"
          securityContext:
            runAsUser: 0
            runAsGroup: 0
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          # Health checks
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8082
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8082
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
      volumes:
        - name: voucher-storage
          persistentVolumeClaim:
            claimName: voucher-storage-pvc
        - name: setup-scripts
          configMap:
            name: evd-files-config
            defaultMode: 0755
        - name: evd-files
          configMap:
            name: evd-files-config
---
# Service
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  type: ClusterIP
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
