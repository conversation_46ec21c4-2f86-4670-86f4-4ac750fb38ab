# PersistentVolumeClaim for voucher management
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-storage-pvc
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  # Uncomment one of the following storage classes based on your cluster:
  # storageClassName: "gp2"              # AWS EBS
  # storageClassName: "standard"         # GKE Standard
  # storageClassName: "fast"             # GKE SSD
  # storageClassName: "managed-premium"  # Azure Premium
  # storageClassName: "vsphere-volume"   # vSphere
  # storageClassName: "local-storage"    # Local storage
  # Leave commented to use default storage class
---
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: oneplatform
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: oneplatform-bp-secret
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:8
          imagePullPolicy: Always
          ports:
            - containerPort: 8082
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
          env:
            - name: TZ
              value: "Africa/Nairobi"
          securityContext:
            runAsUser: 0
            runAsGroup: 0
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          # Health checks
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8082
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8082
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
      volumes:
        - name: voucher-storage
          persistentVolumeClaim:
            claimName: voucher-storage-pvc
      # Ensure pod is scheduled on nodes with sufficient resources
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
---
# Service
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  type: ClusterIP
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
