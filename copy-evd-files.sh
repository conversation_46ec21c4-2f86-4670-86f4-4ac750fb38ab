#!/bin/bash

# <PERSON>ript to copy EVD files to the running container

# Set variables
NAMESPACE="oneplatform"
DEPLOYMENT_NAME="one-platform-evd-distribution-management-jobs"
LOCAL_EVD_PATH="./evd-files"  # Change this to your local EVD files path
CONTAINER_PATH="/app/vouchermanagement/voucherfiles/input"

echo "=== Copying EVD Files to Container ==="

# Get the pod name
POD_NAME=$(kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT_NAME -o jsonpath='{.items[0].metadata.name}')

if [ -z "$POD_NAME" ]; then
    echo "Error: No pod found for deployment $DEPLOYMENT_NAME in namespace $NAMESPACE"
    exit 1
fi

echo "Found pod: $POD_NAME"

# Check if local EVD files directory exists
if [ ! -d "$LOCAL_EVD_PATH" ]; then
    echo "Error: Local EVD files directory $LOCAL_EVD_PATH does not exist"
    echo "Please create the directory and place your EVD files there"
    exit 1
fi

# Create directories in container if they don't exist
echo "Creating directories in container..."
kubectl exec -n $NAMESPACE $POD_NAME -- mkdir -p $CONTAINER_PATH
kubectl exec -n $NAMESPACE $POD_NAME -- mkdir -p $CONTAINER_PATH/backup
kubectl exec -n $NAMESPACE $POD_NAME -- chmod -R 755 /app/vouchermanagement

# Copy all EVD files
echo "Copying EVD files..."
for file in $LOCAL_EVD_PATH/EVD*; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        echo "Copying $filename..."
        kubectl cp "$file" $NAMESPACE/$POD_NAME:$CONTAINER_PATH/$filename
    fi
done

# Verify files were copied
echo "Verifying files in container..."
kubectl exec -n $NAMESPACE $POD_NAME -- ls -la $CONTAINER_PATH

echo "=== EVD Files Copy Complete ==="
