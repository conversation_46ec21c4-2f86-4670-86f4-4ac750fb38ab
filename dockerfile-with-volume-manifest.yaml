# PersistentVolumeClaim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-storage-pvc
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
# Deployment with init container to copy files from image to volume
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: oneplatform
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: oneplatform-bp-secret
      # Init container to copy files from image to persistent volume
      initContainers:
        - name: copy-initial-files
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:9
          command: 
            - /bin/sh
            - -c
            - |
              echo "Setting up directory structure and copying files..."
              
              # Create directories
              mkdir -p /app/vouchermanagement/Key
              mkdir -p /app/vouchermanagement/voucherfiles/input
              mkdir -p /app/vouchermanagement/voucherfiles/input/backup
              
              # Copy EVD files from image to volume (only if they don't exist)
              if [ -d "/app/initial-files/evd" ]; then
                echo "Copying EVD files..."
                for file in /app/initial-files/evd/*EVD*; do
                  if [ -f "$file" ]; then
                    filename=$(basename "$file")
                    if [ ! -f "/app/vouchermanagement/voucherfiles/input/$filename" ]; then
                      cp "$file" "/app/vouchermanagement/voucherfiles/input/"
                      echo "Copied: $filename"
                    else
                      echo "File already exists: $filename"
                    fi
                  fi
                done
              fi
              
              # Copy key files from image to volume (only if they don't exist)
              if [ -d "/app/initial-files/keys" ]; then
                echo "Copying key files..."
                for file in /app/initial-files/keys/*.key; do
                  if [ -f "$file" ]; then
                    filename=$(basename "$file")
                    if [ ! -f "/app/vouchermanagement/Key/$filename" ]; then
                      cp "$file" "/app/vouchermanagement/Key/"
                      echo "Copied: $filename"
                    else
                      echo "File already exists: $filename"
                    fi
                  fi
                done
              fi
              
              # Set permissions
              chmod -R 755 /app/vouchermanagement
              
              echo "Setup complete. Directory contents:"
              echo "=== EVD Files ==="
              ls -la /app/vouchermanagement/voucherfiles/input/
              echo "=== Key Files ==="
              ls -la /app/vouchermanagement/Key/
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
          securityContext:
            runAsUser: 0
            runAsGroup: 0
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:9
          imagePullPolicy: Always
          ports:
            - containerPort: 8082
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
          env:
            - name: TZ
              value: "Africa/Nairobi"
          securityContext:
            runAsUser: 0
            runAsGroup: 0
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          # Health checks
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8082
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8082
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
      volumes:
        - name: voucher-storage
          persistentVolumeClaim:
            claimName: voucher-storage-pvc
---
# Service
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  type: ClusterIP
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
