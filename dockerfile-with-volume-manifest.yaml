# PersistentVolumeClaim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-storage-pvc
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
# Deployment with init container to copy files from image to volume
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: oneplatform
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: oneplatform-bp-secret
      # Init container to copy files from image to persistent volume
      initContainers:
        - name: copy-initial-files
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:10
          command: 
            - /bin/sh
            - -c
            - |
              echo "=== Starting setup ==="
              echo "Checking what's available in the image..."
              ls -la /app/
              ls -la /app/initial-files/ 2>/dev/null || echo "No initial-files directory"

              # Create directories
              echo "Creating directories..."
              mkdir -p /app/vouchermanagement/Key
              mkdir -p /app/vouchermanagement/voucherfiles/input
              mkdir -p /app/vouchermanagement/voucherfiles/input/backup

              # Check if initial-files directory exists and what's in it
              if [ -d "/app/initial-files" ]; then
                echo "Found initial-files directory:"
                find /app/initial-files -type f -name "*EVD*" -o -name "*.key" | head -10

                # Copy EVD files
                echo "Looking for EVD files..."
                find /app/initial-files -name "*EVD*" -type f | while read file; do
                  if [ -f "$file" ]; then
                    filename=$(basename "$file")
                    echo "Copying EVD file: $filename"
                    cp "$file" "/app/vouchermanagement/voucherfiles/input/"
                  fi
                done

                # Copy key files
                echo "Looking for key files..."
                find /app/initial-files -name "*.key" -type f | while read file; do
                  if [ -f "$file" ]; then
                    filename=$(basename "$file")
                    echo "Copying key file: $filename"
                    cp "$file" "/app/vouchermanagement/Key/"
                  fi
                done
              else
                echo "No initial-files directory found!"
              fi

              # Set permissions
              chmod -R 755 /app/vouchermanagement

              echo "=== Setup complete ==="
              echo "EVD Files:"
              ls -la /app/vouchermanagement/voucherfiles/input/
              echo "Key Files:"
              ls -la /app/vouchermanagement/Key/
              echo "=== End setup ==="
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
          securityContext:
            runAsUser: 0
            runAsGroup: 0
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:10
          imagePullPolicy: Always
          ports:
            - containerPort: 8082
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
          env:
            - name: TZ
              value: "Africa/Nairobi"
          securityContext:
            runAsUser: 0
            runAsGroup: 0
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          # Health checks - using TCP probe since /actuator/health returns 404
          livenessProbe:
            tcpSocket:
              port: 8082
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: 8082
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
      volumes:
        - name: voucher-storage
          persistentVolumeClaim:
            claimName: voucher-storage-pvc
---
# Service
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  type: ClusterIP
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
