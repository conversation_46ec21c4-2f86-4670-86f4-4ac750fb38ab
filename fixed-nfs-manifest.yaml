# PersistentVolumeClaim using default storage class
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-storage-pvc
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  # Uses default storage class automatically
---
# Deployment using PVC
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: oneplatform
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: oneplatform-bp-secret
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:8
          imagePullPolicy: Always
          ports:
            - containerPort: 8082
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
          env:
            - name: TZ
              value: "Africa/Nairobi"
          securityContext:
            runAsUser: 0
            runAsGroup: 0
      volumes:
        - name: voucher-storage
          persistentVolumeClaim:
            claimName: voucher-storage-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
