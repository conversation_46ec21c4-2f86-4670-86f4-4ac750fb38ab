apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: oneplatform
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: oneplatform-bp-secret
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:8
          imagePullPolicy: Always
          ports:
            - containerPort: 8082
          volumeMounts:
            - name: nfs-voucher-storage
              mountPath: /app/vouchermanagement
              subPath: vouchermanagement
          env:
            - name: TZ
              value: "Africa/Nairobi"
          # Add security context if needed
          securityContext:
            runAsUser: 0
            runAsGroup: 0
      volumes:
        - name: nfs-voucher-storage
          nfs:
            server: mdc1-psc-nfs.safaricomet.net
            path: /oneplatform
            readOnly: false
      # Add node selector if NFS is only available from certain nodes
      # nodeSelector:
      #   nfs-client: "true"
---
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
