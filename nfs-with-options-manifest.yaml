# PersistentVolume with NFS mount options
apiVersion: v1
kind: PersistentVolume
metadata:
  name: nfs-voucher-pv
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  mountOptions:
    - nfsvers=3
    - tcp
    - intr
    - hard
    - noatime
    - rsize=8192
    - wsize=8192
    - timeo=14
  nfs:
    server: mdc1-psc-nfs.safaricomet.net
    path: /oneplatform
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nfs-voucher-pvc
  namespace: oneplatform
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  volumeName: nfs-voucher-pv
---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: oneplatform
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: oneplatform-bp-secret
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:8
          imagePullPolicy: Always
          ports:
            - containerPort: 8082
          volumeMounts:
            - name: nfs-voucher-storage
              mountPath: /app/vouchermanagement
              subPath: vouchermanagement
          env:
            - name: TZ
              value: "Africa/Nairobi"
      volumes:
        - name: nfs-voucher-storage
          persistentVolumeClaim:
            claimName: nfs-voucher-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
