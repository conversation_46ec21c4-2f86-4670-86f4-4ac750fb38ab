FROM eclipse-temurin:21-jre-alpine

# Timezone
ENV TZ=Africa/Nairobi
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Rudementary
WORKDIR /app

# Application name same as deployment name
LABEL name="one-platform-evd-distribution-management-jobs" \
description="RMS OP Job for voucher processing and activation" \
maintainer="IT Delivery Team"
MAINTAINER "IT Delivery Team"

# Create necessary directories to match volume mount paths
RUN mkdir -p /app/vouchermanagement/Key && \
    mkdir -p /app/vouchermanagement/voucherfiles/input && \
    mkdir -p /app/vouchermanagement/voucherfiles/input/backup && \
    mkdir -p /app/initial-files/evd && \
    mkdir -p /app/initial-files/keys && \
    chmod -R 755 /app/vouchermanagement && \
    chmod -R 755 /app/initial-files

# Copy EVD files and keys to initial-files directory
COPY *EVD* /app/initial-files/evd/
COPY *.key /app/initial-files/keys/

# Port
EXPOSE 8082

# Add App
ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} /app/service.jar

# User mode
USER root

ENTRYPOINT ["java","-jar","/app/service.jar"]