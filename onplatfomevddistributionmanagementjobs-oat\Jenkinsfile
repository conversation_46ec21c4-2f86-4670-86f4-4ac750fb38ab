pipeline {
    agent {
        kubernetes {
            cloud 'kubernetes_mdc1'
            label 'op-backend-agents-no-pvc'
            idleMinutes 5
            defaultContainer 'jnlp'
            yamlFile 'k8_build_agent.yml'
        }
    }

    environment {
        IMAGE = readMavenPom().getArtifactId()
        VERSION = "${BUILD_NUMBER}" // Use Jenkins build number as the version
        // VERSION = 1.0.0
        DEPLOYMENT_FILE_DIR = './deployment'
        IMAGE_FULL_ADDR = 'registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat'
        MANIFEST_URL = 'technology/one-platform/backend/onplatfomevddistributionmanagementjobs.git'
        TARGET_BRANCH = 'oat'
        REPOSITORY_URL = "https://gitlab.safaricomet.net/${MANIFEST_URL}"
        TEAMS_WEBHOOK = 'https://prod-98.westeurope.logic.azure.com:443/workflows/107669d24ff74080965543609cd377d4/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=Z0a1APW5bAzAB0QWYFYjy124wjoo4BWChYgWpe3bfsY'
    }

    stages {
        stage('Notify GitLab') {
            steps {
                script {
               // Get commit details 
                env.COMMITTER_NAME = sh(
                    script: """
                        git config --global --add safe.directory ${env.WORKSPACE}
                        git log -1 --pretty=format:'%an'
                    """,
                    returnStdout: true
                ).trim()

                env.COMMIT_MESSAGE = sh(
                    script: "git log -1 --pretty=format:'%s'",
                    returnStdout: true
                ).trim()
                    def BRANCH_NAME = env.JOB_NAME.tokenize('/')[-1]
                    def JOBNAME = env.JOB_NAME.tokenize('/')[1]
                    def J_NAME = "${JOBNAME}-${BRANCH_NAME}:${env.VERSION}"
                    env.J_NAME = J_NAME
                    env.IMAGE_TAG = env.VERSION

                    echo "Job Name: ${J_NAME}"

                    try {
                        updateGitlabCommitStatus name: 'build', state: 'pending'
                    } catch (Exception e) {
                        echo "GitLab notification failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Build jar file') {
            when { expression { true } }
            steps {
                container('maven') {
                    sh 'mvn -ntp -B -Dmaven.test.skip=true clean package'
                }
            }
        }

        stage('Build Docker Image') {
            when { expression { true } }
            steps {
                container('kaniko') {
                    script {
                        sh """
                            /kaniko/executor \\
                                --dockerfile \$(pwd)/Dockerfile \\
                                --context \$(pwd) \\
                                --destination=registry.tools.blueprint.lab/oneplatform/\${J_NAME} \\
                                --build-arg J_NAME=\${J_NAME} \\
                                --insecure \\
                                --skip-tls-verify
                        """
                    }
                }
            }
        }

    stage('Update deployment file') {
      steps {
       container('maven') {
        script {
          catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
            withCredentials([
              usernamePassword(
                credentialsId: 'oneplatform_svc_account',
                usernameVariable: 'GIT_USERNAME',
                passwordVariable: 'GIT_PASSWORD'
              )
            ]) {
              script {
                // // Get commit details 
                // env.COMMITTER_NAME = sh(
                //     script: """
                //         git config --global --add safe.directory ${env.WORKSPACE}
                //         git log -1 --pretty=format:'%an'
                //     """,
                //     returnStdout: true
                // ).trim()

                // env.COMMIT_MESSAGE = sh(
                //     script: "git log -1 --pretty=format:'%s'",
                //     returnStdout: true
                // ).trim()
                // URL-encode credentials if needed
                env.ENC_USER = java.net.URLEncoder.encode(env.GIT_USERNAME, "UTF-8")
                env.ENC_PASS = java.net.URLEncoder.encode(env.GIT_PASSWORD, "UTF-8")
                env.GIT_PUSH_URL = "https://${env.ENC_USER}:${env.ENC_PASS}@gitlab.safaricomet.net/${MANIFEST_URL}"
              }

              sh '''#!/usr/bin/env bash
              set -e

              # Clean any previous clone and clone fresh
              rm -rf manifests-repo
              git clone --branch ${TARGET_BRANCH} "${GIT_PUSH_URL}" manifests-repo
              cd manifests-repo
              ls -ltr

              #git config user.email "<EMAIL>"
              #git config user.name "one_pltfrm_user"

              echo "Before update:"
              cat "${DEPLOYMENT_FILE_DIR}/deployment.yaml"

              sed -i "s#image: ${IMAGE_FULL_ADDR}:.*#image: ${IMAGE_FULL_ADDR}:${IMAGE_TAG}#g" "${DEPLOYMENT_FILE_DIR}/deployment.yaml"

              echo "After update:"
              cat "${DEPLOYMENT_FILE_DIR}/deployment.yaml"

              git add .
              git commit -m "Done by Jenkins Job changemanifest (${BUILD_NUMBER})" || echo "No changes to commit"

              # git push "${GIT_PUSH_URL}" HEAD:refs/heads/${TARGET_BRANCH}
              git push origin HEAD:${TARGET_BRANCH}
              '''
            }
          }
        }
      }
      }
    }

        // stage('Trigger ManifestUpdate') {
        //     when { expression { true } }
        //     steps {
        //         script {
        //             try {
        //                 build job: 'It-delivery-one-platform/manifest-updater', parameters: [
        //                     string(name: 'IMAGE_TAG', value: "${VERSION}"),
        //                     string(name: 'MANIFEST_URL', value: "${MANIFEST_URL}"),
        //                     string(name: 'DEPLOYMENT_FILE_DIR', value: "${DEPLOYMENT_FILE_DIR}"),
        //                     string(name: 'TARGET_BRANCH', value: "${TARGET_BRANCH}"),
        //                     string(name: 'IMAGE_FULL_ADDR', value: "${IMAGE_FULL_ADDR}"),
        //                     string(name: 'REPOSITORY_URL', value: "${REPOSITORY_URL}"),
        //                     string(name: 'IMAGE_NAME', value: "${J_NAME}")
        //                 ]
        //             } catch (Exception e) {
        //                 echo "Manifest update failed: ${e.getMessage()}"
        //             }
        //         }
        //     }
        // }
    }
    post {
    always {
        container('maven') {
            script {
                // Determine build status
                 // Capture Git commit info (ensure repository is checked out first)

                //  env.COMMITTER_NAME = sh(
                //     script: """
                //         git config --global --add safe.directory ${env.WORKSPACE}
                //         git log -1 --pretty=format:'%an'
                //     """,
                //     returnStdout: true
                // ).trim()

                // env.COMMIT_MESSAGE = sh(
                //     script: "git log -1 --pretty=format:'%s'",
                //     returnStdout: true
                // ).trim()

                def buildStatus = currentBuild.currentResult ?: 'SUCCESS'
                def themeColor = buildStatus == 'SUCCESS' ? 'good' :
                                 buildStatus == 'FAILURE' ? 'attention' :
                                 'warning' // yellow for UNSTABLE
                def statusEmoji

                if (buildStatus == 'SUCCESS') {
                    themeColor = 'good'
                    statusEmoji = '✅'
                } else if (buildStatus == 'FAILURE') {
                    themeColor = 'attention'
                    statusEmoji = '❌'
                } else { // UNSTABLE or other
                    themeColor = 'warning'
                    statusEmoji = '⚠️'
                }
                // Construct Adaptive Card payload
                def payload = """
                {
                  "type": "message",
                  "attachments": [
                    {
                      "contentType": "application/vnd.microsoft.card.adaptive",
                      "content": {
                        "\$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                        "type": "AdaptiveCard",
                        "version": "1.4",
                        "body": [
                          {
                            "type": "TextBlock",
                            "size": "Large",
                            "weight": "Bolder",
                            "text": "${statusEmoji} Build Pipeline ${buildStatus == 'SUCCESS' ? 'Success' : buildStatus == 'FAILURE' ? 'Failed' : 'Unstable'}",
                            "color": "${themeColor}"
                          },
                          {
                            "type": "TextBlock",
                            "text": "Environment: **${env.TARGET_BRANCH}**",
                            "wrap": true
                          },
                          {
                            "type": "FactSet",
                            "facts": [
                              { "title": "Build Number", "value": "${env.BUILD_NUMBER}" },
                              { "title": "Status", "value": "${buildStatus}" },
                              { "title": "Image Name", "value": "${J_NAME}" },
                              { "title": "Committer", "value": "${env.COMMITTER_NAME ?: 'N/A'}" },
                              { "title": "Commit Message", "value": "${env.COMMIT_MESSAGE ?: 'N/A'}" },
                              { "title": "Job", "value": "${env.JOB_NAME}" }
                            ]
                          }
                        ],
                        "actions": [
                          {
                            "type": "Action.OpenUrl",
                            "title": "🔗 View Build",
                            "url": "${env.BUILD_URL}"
                          }
                        ]
                      }
                    }
                  ]
                }
                """

                // Send payload using curl
                sh """
                  curl -X POST \\
                      -H "Content-Type: application/json" \\
                      -d '${payload}' \\
                      "${TEAMS_WEBHOOK}"
                """
            }
        }
     }
}  
}