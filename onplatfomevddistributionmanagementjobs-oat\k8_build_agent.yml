apiVersion: v1
kind: Pod
metadata:
  name: k8-agent-containers
  namespace: jenkins
  labels:
    job: mpesa-k8-agents
spec:
  serviceAccountName: jenkins-admin
  containers:
    - name: jnlp
      image: mdc1-sfcr.safaricomet.net/dev-tools/jenkins-inbound-agent:custom3
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "900m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
    - name: maven
      image: mdc1-sfcr.safaricomet.net/dev-tools/maven-builder:offline
      command: ["tail", "-f", "/dev/null"]
      env:
        - name: MAVEN_OPTS
          value: "-Xmx512m"
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "800Mi"
          cpu: "120m"
      volumeMounts:
        - name: shared-rw-vol
          mountPath: /root/.m2
        - name: ca-cert
          mountPath: /etc/ssl/certs
    - name: kaniko
      image: mdc1-sfcr.safaricomet.net/dev-tools/kaniko-debug-local:offline
      command: ["cat"]
      tty: true
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "1"
      env:
        - name: DOCKER_CONFIG
          value: /.docker
      volumeMounts:
        - name: docker-config
          mountPath: /.docker
  imagePullSecrets:
    - name: harbor-registry-secret
  restartPolicy: Never
  volumes:
    - name: ca-cert
      configMap:
        name: safaricomet.net
        items:
          - key: ca-certificates.crt
            path: ca-certificates.crt
    - name: docker-config
      secret:
        secretName: oneplatform-bp-secret
        items:
          - key: .dockerconfigjson
            path: config.json
    - name: workspace-volume
      emptyDir: {}
    - name: shared-rw-vol
      persistentVolumeClaim:
        claimName: shared-devops-rw-pvc