package com.example.RMSOPJOB.Quires;

import com.example.RMSOPJOB.config.DBconfig;
import com.example.RMSOPJOB.dto.DBResponse.FullResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.postgresql.copy.CopyManager;
import org.postgresql.core.BaseConnection;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.io.StringReader;
import java.sql.SQLException;

@Service
public class CustomQueries {

    private final DBconfig dbConfig;
    private final JdbcTemplate jdbcTemplate;
    public CustomQueries(DBconfig dbConfig) throws SQLException {
        this.dbConfig = dbConfig;
        this.jdbcTemplate = new JdbcTemplate(dbConfig.dataSource());
    }


    public Long BatchInsertRMSVouchers(String data) throws Exception{
        CopyManager copyManager = new CopyManager((BaseConnection) dbConfig.createBaseConnection());
        StringReader reader = new StringReader(data);
        String BatchCopy = "COPY public.available_vouchers (serial_number, enc_pin, batch_number, stopdate, facevalue, vouchertype, status,loaded_date,is_active,reuse_count) FROM STDIN WITH (FORMAT csv)";
        return  copyManager.copyIn(BatchCopy, reader);}
}
