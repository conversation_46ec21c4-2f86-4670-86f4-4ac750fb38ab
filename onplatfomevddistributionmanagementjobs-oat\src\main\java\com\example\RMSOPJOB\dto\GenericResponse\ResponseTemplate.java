package com.example.RMSOPJOB.dto.GenericResponse;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResponseTemplate<T> {


    private String statusCode;
    private String statusMessage;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private T data;

}
