package com.example.RMSOPJOB.dto.tibcoactivatecardbySN.requestpayload;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoucherActivationVBO {
  @JsonProperty("IDs")
  private IDs  IDs;
  @JsonProperty("Details")
    private Details Details;
  @JsonProperty("Parts")
    private Parts Parts;
  @JsonProperty("RelatedParties")
    private RelatedParties RelatedParties;
}
