package com.example.RMSOPJOB.dto.tibcoactivatecardbySN.responsepayload;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoucherActivationVBO {
    @JsonProperty("IDs")
    private List<IDdetails> IDs;
    @JsonProperty("StatusCode")
    private String StatusCode;
    @JsonProperty("Desc")
    private String Desc;
    @JsonProperty("systemID")
    private String systemID;

}
