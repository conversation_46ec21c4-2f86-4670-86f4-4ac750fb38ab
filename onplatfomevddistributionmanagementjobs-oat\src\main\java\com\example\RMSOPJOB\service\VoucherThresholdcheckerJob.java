package com.example.RMSOPJOB.service;

import com.example.RMSOPJOB.Quires.GenericQueries;
import org.springframework.scheduling.annotation.Scheduled;

public class VoucherThresholdcheckerJob {

    private final GenericQueries genericQueries;

    public VoucherThresholdcheckerJob(GenericQueries genericQueries) {
        this.genericQueries = genericQueries;
    }

  //  @Scheduled(cron = "${ActivateCard.schedule.cron}")
    public void movedUtilizedToBackup(){
    String query= """
            update public.vouchermanagment_tbl e set status=3\s
            from public.opvouchermanagementconfigration_tbl t
            where\s
            t.delivery_type = e.delivery_type and
            status =2 and EXTRACT(EPOCH FROM (now()::timestamp-deliveredat) / 3600.0) >t.duration
            returning e.serial_number;
            
            """;
     //genericQueries.selectExecutor(query,);

    }
}
