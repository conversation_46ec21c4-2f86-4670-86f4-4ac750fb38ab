package com.example.RMSOPJOB.utils;




import com.example.RMSOPJOB.dto.DBResponse.FullResponse;
import com.example.RMSOPJOB.dto.DecryptionDto.DecryptionResponse;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.EncodedKeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.List;

public class MySecurity {
    protected Cipher rcms_Cipher;
    protected String ALGORITHM = "AES";
    protected boolean EncryptMode = true;
    protected boolean isCipherReady = false;
    protected SecretKey Key_des;
    protected String DES_TRANSFORMATION = "DES/ECB/NoPadding";
    protected String key_string_des;
    protected final String DEFAULT_KEY_STRING_DES = "4A60E6CA945DF0BC";
    protected final int KEY_LENGTH_DES = 64;
    protected SecretKey Key_aes;
    protected String AES_TRANSFORMATION = "AES/ECB/PKCS5Padding";
    protected String key_string_aes;
    protected final String DEFAULT_KEY_STRING_AES = "436026BF241D276C43602eBFE40D276C14A60EBC241D970C436026BF241D276C";
    protected final int KEY_LENGTH_AES = 256;
    protected boolean isPub_key = true;
    protected PublicKey Key_rsa_pub;
    protected PrivateKey Key_rsa_pri;
    protected String RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    protected String key_string_rsa_pub;
    protected String key_string_rsa_pri;
    protected int KEY_LENGTH_RSA;
    protected final int RSA_ENC_BLOCK_LENGTH = 100;
    protected int RSA_DEC_BLOCK_LENGTH;

    public MySecurity(int key_length_rsa) {
        this.set_RSA_Key_Length(key_length_rsa);
    }

    public int get_RSA_Key_Length() {
        return this.KEY_LENGTH_RSA;
    }

    public void set_RSA_Key_Length(int key_length_rsa) {
        this.KEY_LENGTH_RSA = key_length_rsa;
        this.RSA_DEC_BLOCK_LENGTH = this.KEY_LENGTH_RSA / 8;
    }

    public void set_des(String key) {
        this.isCipherReady = false;
        this.ALGORITHM = "DES";
        if (key != null && key.length() == 16 && isValidHexString(key)) {
            this.key_string_des = key.toUpperCase();
        } else {
            this.key_string_des = "4A60E6CA945DF0BC";
        }

    }

    public String getKey_aes() {
        return this.key_string_aes;
    }

    public void set_aes(byte[] key) {
        this.isCipherReady = false;
        this.ALGORITHM = "AES";
        if (key != null && key.length == 32) {
            this.key_string_aes = byte2hex(key);
        } else {
            this.key_string_aes = "436026BF241D276C43602eBFE40D276C14A60EBC241D970C436026BF241D276C";
        }

    }

    public void set_aes(String key) {
        this.isCipherReady = false;
        this.ALGORITHM = "AES";
        if (key != null && key.length() == 64 && isValidHexString(key)) {
            this.key_string_aes = key.toUpperCase();
        } else {
            this.key_string_aes = "436026BF241D276C43602eBFE40D276C14A60EBC241D970C436026BF241D276C";
        }

    }

    public String getKey_rsa_pri() {
        return this.key_string_rsa_pri;
    }

    public String getKey_rsa_pub() {
        return this.key_string_rsa_pub;
    }

    public void set_rsa(byte[] key, boolean isPub) {
        this.isCipherReady = false;
        if (key != null && key.length != 0) {
            this.ALGORITHM = "RSA";
            this.isPub_key = isPub;
            if (this.isPub_key) {
                this.key_string_rsa_pub = byte2hex(key);
            } else {
                this.key_string_rsa_pri = byte2hex(key);
            }

        }
    }

    public void set_rsa(String key, boolean isPub) {
        this.isCipherReady = false;
        if (key != null && (key.length() != 0 || !isValidHexString(key))) {
            this.ALGORITHM = "RSA";
            this.isPub_key = isPub;
            if (this.isPub_key) {
                this.key_string_rsa_pub = key.toUpperCase();
            } else {
                this.key_string_rsa_pri = key.toUpperCase();
            }

        }
    }









    public void InitCipherHandler() throws Exception {
        if (this.ALGORITHM.equalsIgnoreCase("DES")) {
            if (this.key_string_des == null || this.key_string_des.equalsIgnoreCase("")) {
                return;
            }

            this.rcms_Cipher = Cipher.getInstance(this.DES_TRANSFORMATION);
            this.Key_des = new SecretKeySpec(hex2byte(this.key_string_des), "DES");
            if (this.EncryptMode) {
                this.rcms_Cipher.init(1, this.Key_des);
            } else {
                this.rcms_Cipher.init(2, this.Key_des);
            }
        } else if (this.ALGORITHM.equalsIgnoreCase("AES")) {
            if (this.key_string_aes == null || this.key_string_aes.equalsIgnoreCase("")) {
                return;
            }

            this.rcms_Cipher = Cipher.getInstance(this.AES_TRANSFORMATION);
            this.Key_aes = new SecretKeySpec(hex2byte(this.key_string_aes), "AES");
            if (this.EncryptMode) {
                this.rcms_Cipher.init(1, this.Key_aes);
            } else {
                this.rcms_Cipher.init(2, this.Key_aes);
            }
        } else if (this.ALGORITHM.equalsIgnoreCase("RSA")) {
            this.rcms_Cipher = Cipher.getInstance(this.RSA_TRANSFORMATION);
            KeyFactory keyFact = KeyFactory.getInstance("RSA");
            if (this.isPub_key) {
                if (this.key_string_rsa_pub == null || this.key_string_rsa_pub.equalsIgnoreCase("")) {
                    return;
                }

                EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(hex2byte(this.key_string_rsa_pub));
                this.Key_rsa_pub = keyFact.generatePublic(publicKeySpec);
                if (this.EncryptMode) {
                    this.rcms_Cipher.init(1, this.Key_rsa_pub);
                } else {
                    this.rcms_Cipher.init(2, this.Key_rsa_pub);
                }
            } else {
                if (this.key_string_rsa_pri == null || this.key_string_rsa_pri.equalsIgnoreCase("")) {
                    return;
                }

                EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(hex2byte(this.key_string_rsa_pri));
                this.Key_rsa_pri = keyFact.generatePrivate(privateKeySpec);
                if (this.EncryptMode) {
                    this.rcms_Cipher.init(1, this.Key_rsa_pri);
                } else {
                    this.rcms_Cipher.init(2, this.Key_rsa_pri);
                }
            }
        }

        this.isCipherReady = true;
    }






    public byte[] encode(byte[] data) throws Exception {
        if (data != null && data.length != 0) {
            if (!this.isCipherReady || !this.EncryptMode) {
                this.EncryptMode = true;
                this.InitCipherHandler();
            }

            if (!this.isCipherReady) {
                return data;
            } else if (this.ALGORITHM.equalsIgnoreCase("DES")) {
                int mod_len = data.length / 8;
                int act_len = 0;
                if (mod_len * 8 == data.length) {
                    act_len = mod_len * 8;
                } else {
                    act_len = (mod_len + 1) * 8;
                }

                byte[] buff = new byte[act_len];
                System.arraycopy(data, 0, buff, 0, data.length);
                return this.rcms_Cipher.doFinal(buff);
            } else if (this.ALGORITHM.equalsIgnoreCase("AES")) {

                return this.rcms_Cipher.doFinal(data);
            } else if (!this.ALGORITHM.equalsIgnoreCase("RSA")) {
                return data;
            } else {
                int iblock_num = data.length / 100;
                int imod_num = data.length % 100;
                iblock_num = imod_num == 0 ? iblock_num : iblock_num + 1;
                byte[] enc_bytes = new byte[iblock_num * this.RSA_DEC_BLOCK_LENGTH];
                byte[] org_block = new byte[100];
                int ibyte_count = 0;

                for(int ienc_Index = 0; ienc_Index < iblock_num; ++ienc_Index) {
                    if (ienc_Index == iblock_num - 1 && imod_num > 0) {
                        org_block = new byte[imod_num];
                        this.CopyByteArray(data, ienc_Index * 100, org_block, 0, imod_num);
                    } else {
                        this.CopyByteArray(data, ienc_Index * 100, org_block, 0, 100);
                    }

                    byte[] enc_block = this.rcms_Cipher.doFinal(org_block);
                    this.CopyByteArray(enc_block, 0, enc_bytes, ibyte_count, enc_block.length);
                    ibyte_count += enc_block.length;
                }

                return enc_bytes;
            }
        } else {
            return data;
        }
    }













    public String encode(String data) throws Exception {
        if (data != null && !data.equalsIgnoreCase("")) {
            byte[] b = this.encode(hex2byte(data));
            return byte2hex(b);
        } else {
            return "";
        }
    }








    public byte[] decode(byte[] data) throws Exception {
        if (data != null && data.length != 0) {
            if (!this.isCipherReady || this.EncryptMode) {
                this.EncryptMode = false;
                this.InitCipherHandler();
            }

            if (!this.isCipherReady) {
                return data;
            } else if (this.ALGORITHM.equalsIgnoreCase("DES")) {
                return this.rcms_Cipher.doFinal(data);
            } else if (this.ALGORITHM.equalsIgnoreCase("AES")) {

                return this.rcms_Cipher.doFinal(data);
            } else if (!this.ALGORITHM.equalsIgnoreCase("RSA")) {
                return data;
            } else {
                int iblock_num = data.length / this.RSA_DEC_BLOCK_LENGTH;
                byte[] dec_bytes_tmp = new byte[iblock_num * 100];
                byte[] enc_block = new byte[this.RSA_DEC_BLOCK_LENGTH];
                int ibyte_count = 0;

                for(int ienc_Index = 0; ienc_Index < iblock_num; ++ienc_Index) {
                    this.CopyByteArray(data, ienc_Index * this.RSA_DEC_BLOCK_LENGTH, enc_block, 0, this.RSA_DEC_BLOCK_LENGTH);
                    byte[] dec_block = this.rcms_Cipher.doFinal(enc_block);
                    this.CopyByteArray(dec_block, 0, dec_bytes_tmp, ibyte_count, dec_block.length);
                    ibyte_count += dec_block.length;
                }

                byte[] dec_bytes = new byte[ibyte_count];
                this.CopyByteArray(dec_bytes_tmp, 0, dec_bytes, 0, ibyte_count);
                return dec_bytes;
            }
        } else {
            return data;
        }
    }










    public String decode(String data) throws Exception {
        if (data != null && !data.equalsIgnoreCase("")) {
            byte[] s = this.decode(hex2byte(data));
            return byte2hex(s);
        } else {
            return "";
        }
    }








    public static String byte2hex(byte[] b) {
        String hs = "";
        String stmp = "";

        for(int i = 0; i < b.length; ++i) {
            stmp = Integer.toHexString(b[i] & 255);
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
        }

        return hs.toUpperCase();
    }










    public static byte[] hex2byte(String hex) throws Exception {
        if (hex.length() % 2 != 0) {
            throw new IllegalArgumentException();
        } else {
            char[] arr = hex.toCharArray();
            byte[] b = new byte[hex.length() / 2];
            int i = 0;
            int j = 0;

            for(int l = hex.length(); i < l; ++j) {
                int var10001 = i++;
                String swap = "" + arr[var10001] + arr[i];
                int byteint = Integer.parseInt(swap, 16) & 255;
                b[j] = Integer.valueOf(byteint).byteValue();
                ++i;
            }

            return b;
        }
    }












    public static boolean isValidHexString(String hex) {
        if (hex == null) {
            return false;
        } else if (hex.equalsIgnoreCase("")) {
            return false;
        } else if (hex.length() % 2 != 0) {
            return false;
        } else {
            byte[] bstr = hex.getBytes();

            for(int i = 0; i < bstr.length; ++i) {
                byte cHex = bstr[i];
                if (cHex != 48 && cHex != 49 && cHex != 50 && cHex != 51 && cHex != 52 && cHex != 53 && cHex != 54 && cHex != 55 && cHex != 56 && cHex != 57 && cHex != 65 && cHex != 66 && cHex != 67 && cHex != 68 && cHex != 69 && cHex != 70 && cHex != 97 && cHex != 98 && cHex != 99 && cHex != 100 && cHex != 101 && cHex != 102) {
                    return false;
                }
            }

            return true;
        }
    }










    public int CopyByteArray(byte[] src, int isrc_pos, byte[] dest, int idest_pos, int iLength) {
        if (src != null && isrc_pos <= src.length && dest != null && idest_pos <= dest.length) {
            int iMax_length = iLength;
            if (isrc_pos + iLength > src.length) {
                iMax_length = src.length - isrc_pos;
            }

            if (idest_pos + iMax_length > dest.length) {
                iMax_length = dest.length - idest_pos;
            }

            for(int i = 0; i < iMax_length; ++i) {
                dest[idest_pos + i] = src[isrc_pos + i];
            }

            return iMax_length;
        } else {
            return 0;
        }
    }













    public String toString() {
        String s;
        if (this.ALGORITHM.equalsIgnoreCase("AES")) {
            s = new String("AES(" + this.AES_TRANSFORMATION + ") key: " + this.key_string_aes);
        } else if (this.ALGORITHM.equalsIgnoreCase("RSA")) {
            if (this.isPub_key) {
                s = new String("RSA(" + this.RSA_TRANSFORMATION + ") public key: " + this.key_string_rsa_pub);
            } else {
                s = new String("RSA(" + this.RSA_TRANSFORMATION + ") private key: " + this.key_string_rsa_pri);
            }
        } else {
            s = "ERROR: unsupported algorithm!";
        }

        return s;
    }








    public static String getTimeStampString(Timestamp ts) {
        if (ts == null) {
            return "";
        } else {
            Calendar c = Calendar.getInstance();
            c.setTime(ts.getTimestamp());
            int month = c.get(2) + 1;
            String mm = "" + month;
            if (mm.length() < 2) {
                mm = "0" + mm;
            }

            String dd = "" + c.get(5);
            if (dd.length() < 2) {
                dd = "0" + dd;
            }

            int yyyy = c.get(1);
            String hh = "" + c.get(11);
            if (hh.length() < 2) {
                hh = "0" + hh;
            }

            String min = "" + c.get(12);
            if (min.length() < 2) {
                min = "0" + min;
            }

            String ss = "" + c.get(13);
            if (ss.length() < 2) {
                ss = "0" + ss;
            }

            String sTimeString = String.valueOf(yyyy) + "." + mm + "." + dd + "." + hh + "." + min + "." + ss;
            return sTimeString;
        }
    }







    public static void trace(boolean iftrace, String str) {
        if (iftrace) {
            System.out.println(str);
        }

    }

    /// Decypt pin

    public FullResponse PinDec(FullResponse fullResponse,
                               String decAESKey ) throws Exception {

        try {
            boolean verbose = false;
            MySecurity decryppin= new MySecurity(2048);

            System.out.println("AESKey:"+decAESKey);
            decryppin.set_aes(decAESKey);
            for( List<String> enc:fullResponse.getDetails()){
                enc.set(1,new String(decryppin.decode(hex2byte(enc.get(1)))));
            }
            return fullResponse;
        } catch (Exception e) {
            System.out.println("error");
            return new FullResponse("500","Internal server error, count of reverted serials",0,null);

        }

    }













    ////Read File and Decrypt


    public DecryptionResponse EVDCrypto(String RSAPrivate_filepath,
                                        String RSAPublic_filepath,
                                        String filepath,
                                        String AESKey  ) throws Exception {
        StringBuffer FinalString= new StringBuffer();


        try {
            String des_key = null;
            String enc_method = "AES_RSA";
            String file_name = filepath;
            int rsa_pub_key_length = 2048;
            int mft_pri_key_length = 2048;
            boolean verbose = false;
            String pri_key_file_name = RSAPrivate_filepath;
            String pub_key_file_name=RSAPublic_filepath;


            if ((pri_key_file_name == null || pub_key_file_name == null)) {
                System.out.println(" input rsa key file");
                System.exit(0);
            }

            if (file_name == null) {
                System.out.println(" input encrypted file name.");
                System.exit(0);
            }


            MySecurity decoder = new MySecurity(rsa_pub_key_length);
            BufferedReader br = null;
            if (enc_method.equalsIgnoreCase("AES_RSA")) {
                trace(verbose, "initialize mft private key cipher...");
                BufferedReader var21 = new BufferedReader(new FileReader(pri_key_file_name));
                String str_mfg_pri = var21.readLine();
                var21.close();
                trace(verbose, "MFG Private key(read from file): " + str_mfg_pri);
                MySecurity mft_pri_decoder = new MySecurity(mft_pri_key_length);
                mft_pri_decoder.set_rsa(str_mfg_pri, false);
                trace(verbose, "-----------------------------------------");
                trace(verbose, "initialize rcms public key cipher...");
                BufferedReader var22 = new BufferedReader(new FileReader(pub_key_file_name));
                String str_rcms_pub = var22.readLine();
                var22.close();
                trace(verbose, "RCMS Public key(read from file): " + str_rcms_pub);
                MySecurity rms_pub_decoder = new MySecurity(rsa_pub_key_length);
                rms_pub_decoder.set_rsa(str_rcms_pub, true);
                trace(verbose, "-----------------------------------------");
                br = new BufferedReader(new FileReader(file_name));
                String encrypted_time_flag = br.readLine();
                trace(verbose, "encrypted time flag(read from file): " + encrypted_time_flag);
                String str_aes_enc = br.readLine();
                trace(verbose, "Encrypted AES key(read from file): " + str_aes_enc);
                trace(verbose, "-----------------------------------------");
                trace(verbose, "begin to decrypt AES random key...");
                String str_aes = rms_pub_decoder.decode(mft_pri_decoder.decode(str_aes_enc));
                trace(verbose, "AES key(decrypted): " + str_aes);
                decoder.set_aes(str_aes);
                trace(verbose, "-----------------------------------------");
            } else if (enc_method.equalsIgnoreCase("DES_ONLY")) {
                br = new BufferedReader(new FileReader(file_name));
                decoder.set_des(byte2hex(des_key.getBytes()));
            }


            trace(verbose, "begin to decrypt encrypted:");
            StringBuffer out_buffer = new StringBuffer();
            MySecurity pinencdyc= new MySecurity(2048);
            pinencdyc.set_aes(AESKey);
            String VoucherType= new String();String FaceValue = null;String StopDate = null;
            String Batchno = null;String []  EVD=null; String pin=null; String serial =null;
            String quantity = null;
            StringBuffer headerinfo=new StringBuffer();
            int serial_count= 1;
            for(String line_in = br.readLine(); line_in != null; line_in = br.readLine()) {
               // trace(verbose, ">>>" + line_in);
                String line_out = new String(decoder.decode(hex2byte(line_in)));
                trace(verbose,  line_out);

                if (line_out.contains("DateRequested for delivery")||
                        line_out.contains("ManufacturerContractNumber")||
                line_out.contains("ManufacturerOrderNumber")
                ||line_out.contains("Batch_Size")||line_out.contains("Pin_length")||
                line_out.contains("NumberOfCards")){

                    headerinfo.append(line_out).append(",");
               }
                else if (line_out.contains("[filehdr]")) {
                    //do nothing
                }else if (line_out.contains("CardType")) {
                   VoucherType=line_out.split("=")[1];
                FaceValue=String.valueOf(Integer.parseInt(VoucherType.split("VD")[1]));
                } else if (line_out.contains("CardSerialNumber=")) {
                    serial=line_out.split("=")[1].replace("-","");
                }else if (line_out.contains("Batch/Card Expiry Date")) {
                   StopDate=line_out.split("=")[1];
                }else if (line_out.contains("BatchSerialNumber=")) {
                    Batchno=line_out.split("=")[1];

                }else if (line_out.contains("CardPin=")){
                    pin = byte2hex(pinencdyc.encode(line_out.split("=")[1].getBytes(StandardCharsets.UTF_8)));
                    FinalString.append(serial).append(",").append(pin).append(",").append(Batchno).append(",")
                            .append(StopDate).append(",").append(FaceValue).append(",").append(VoucherType)
                            .append(",").append("1").append(",").append(LocalDateTime.now()).append(",")
                            .append("false").append(",").append(0).append("\n");
                }else {

                }
                    }
            System.out.println("Files with"+headerinfo+ "has been Processed at"+LocalDateTime.now());
            br.close();

        } catch (Exception e) {

            System.out.println("Exception: " + e.getMessage());
            e.printStackTrace();
            return new DecryptionResponse("500",null);
        }
        System.out.println("final Ending:"+ LocalDateTime.now());
        return new DecryptionResponse("200",FinalString);
    }
}
