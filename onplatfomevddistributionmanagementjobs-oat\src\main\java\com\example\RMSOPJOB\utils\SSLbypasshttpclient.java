package com.example.RMSOPJOB.utils;

import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import reactor.netty.http.client.HttpClient;

import javax.net.ssl.SSLException;

public class SSLbypasshttpclient {

    private SSLbypasshttpclient() {
        // private constructor to prevent instantiation
    }
    public static HttpClient create() throws Exception{
        try {
            return HttpClient.create()
                    .secure(sslContextSpec -> {
                        try {
                            sslContextSpec.sslContext(
                                    SslContextBuilder.forClient()
                                            .trustManager(InsecureTrustManagerFactory.INSTANCE)
                                            .build()
                            );
                        } catch (SSLException e) {
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            throw new RuntimeException("Error setting up insecure SSL context", e);
        }
    }


}
