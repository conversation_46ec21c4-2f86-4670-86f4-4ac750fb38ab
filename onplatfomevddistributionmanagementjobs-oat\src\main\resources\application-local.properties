server.port:8082
JdbcUrl=*****************************************
setUser=postgres
setPassword=test
setLoginTimeout=120
DriverClass=org.postgresql.Driver
RSAPUB=C:\\Users\\<USER>\\Desktop\\Brook\\New folder\\New folder\\rms2048pub.key
OPPRI=C:\\Users\\<USER>\\Desktop\\Brook\\New folder\\New folder\\Ellamstest_private_key.key
pinenckey=B288D9261F14C7A4A2BFCE17B7D44FA62369AA519E7A02C703B0240BB67A7DAB
filepath=C:\\Users\\<USER>\\Desktop\\Brook\\New folder\\
backup=C:\\Users\\<USER>\\Desktop\\Brook\\New folder\\backup\\
clinet.Tibco.truststoreURL=C:\\Users\\<USER>\\Desktop\\Truststore.jks
clinet.Tibco.truststorepassword=changeit
clinet.Tibco.TokenUsername=PRETUPS
clinet.Tibco.TokenPassword=MRyhsEPWzO8jDqaE0EAKnw==
clinet.Tibco.TokenURI=/v1/token
clinet.Tibco.TokenBaseURI=https://tibcobp.blueprint.lab/
client.Tibco.token.x-source-system=STEP
client.Tibco.token.x-source-identity-token=U2FmYXJpY29tOmUyZTplc2JldDpBdXRvbWF0aW9u
client.Tibco.activatecardbysn.x-source-system=SND
client.Tibco.activatecardbysn.x-route-id=creditlimit
clinet.Tibco.activationURI=/auth/v1/voucher-activation-service
clinet.Tibco.activationBaseURI=https://tibcobp.blueprint.lab/
clinet.RMSsuccsscode=122
clinet.RMSalreadyActivecode=204
#runs every 10minutes between 00:00 Midnight - 3AM
LoadRMSVoucher.schedule.cron= * */5 * * * *
 #0 */20 0-2 * * *
#runs every 1 hour
ActivateCard.schedule.cron=* */10 * * * *
  #0 0 * * * *







