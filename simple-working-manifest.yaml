# PersistentVolumeClaim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-storage-pvc
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
# Deployment with init container to copy files from image to volume
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: oneplatform
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: oneplatform-bp-secret
      # Init container to copy files from image to persistent volume
      initContainers:
        - name: copy-initial-files
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:10
          command: 
            - /bin/sh
            - -c
            - |
              echo "=== Starting file copy ==="
              
              # Create directories
              mkdir -p /app/vouchermanagement/Key
              mkdir -p /app/vouchermanagement/voucherfiles/input
              mkdir -p /app/vouchermanagement/voucherfiles/input/backup
              
              # Debug: Check what's in the image
              echo "Checking image contents:"
              ls -la /app/
              find /app -name "*EVD*" -o -name "*.key" 2>/dev/null | head -10
              
              # Copy all EVD files (look in multiple possible locations)
              echo "Copying EVD files..."
              find /app -name "*EVD*" -type f 2>/dev/null | while read file; do
                if [ -f "$file" ]; then
                  filename=$(basename "$file")
                  echo "Copying: $filename"
                  cp "$file" "/app/vouchermanagement/voucherfiles/input/"
                fi
              done
              
              # Copy all key files
              echo "Copying key files..."
              find /app -name "*.key" -type f 2>/dev/null | while read file; do
                if [ -f "$file" ]; then
                  filename=$(basename "$file")
                  echo "Copying: $filename"
                  cp "$file" "/app/vouchermanagement/Key/"
                fi
              done
              
              # Set permissions
              chmod -R 755 /app/vouchermanagement
              
              echo "=== Copy complete ==="
              echo "EVD files in volume:"
              ls -la /app/vouchermanagement/voucherfiles/input/
              echo "Key files in volume:"
              ls -la /app/vouchermanagement/Key/
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
          securityContext:
            runAsUser: 0
            runAsGroup: 0
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: registry.tools.blueprint.lab/oneplatform/one-platform-evd-distribution-job-oat:10
          imagePullPolicy: Always
          ports:
            - containerPort: 8082
          volumeMounts:
            - name: voucher-storage
              mountPath: /app/vouchermanagement
          env:
            - name: TZ
              value: "Africa/Nairobi"
          securityContext:
            runAsUser: 0
            runAsGroup: 0
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          # Simple TCP health checks (no HTTP endpoints)
          livenessProbe:
            tcpSocket:
              port: 8082
            initialDelaySeconds: 120
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 5
          readinessProbe:
            tcpSocket:
              port: 8082
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 5
      volumes:
        - name: voucher-storage
          persistentVolumeClaim:
            claimName: voucher-storage-pvc
---
# Service
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: oneplatform
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  type: ClusterIP
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
